import { defineStore } from 'pinia'
import { PROMOTION_POPUP_METHOD } from '~/constants/common'

export const useModalStore = defineStore('modalStore', {
    state: () => ({
        showLoginModal: false,
        showRegisterModal: false,
        showForgotPasswordModal: false,
        showResetPasswordModal: true,
        showPromotionDetail: false,
        showLoginFailModal: false,
        showModalConfirmLogout: false,
        showModalVerifyTelegram: false,
        showModalVerifyTelegramResult: false,
        showRegisterSuccessModal: false,
        showConfirmCancelDepositModal: false,
        showModalNotificationWithdrawCrypto: false,
        showModalWithdrawCryptoSuccess: false,
        showModalWithdrawCryptoError: false,
        showMaintenanceBankFlexPayModal: false,
        showRenewPasswordTelegramModal: false,
        showInsuranceAmountModal: false,
        showInsuranceNotifyModal: false,
        modalQueue: [] as string[],
        currentModal: null as string | null,
        showAddBankModal: false,
        showAddBankSuccessModal: false,
        showEventStageRewardModal: false,
        showUpdateFullNameModal: false,
        showNotifyCodePayModal: false,
        showChangePasswordModal: false,
        showWarningDeposit: false,
        showGameMaintain: false,
        showWarningPromotion: false,
        showPromotionsNotify: false,
        showPromotionsInfo: false,
        showPromotionsCancel: false,
        showPromotionsCancelWarning: false,
        showLotteryBetting: false,
        showModalConfirmBetting: false,
        showModalCancelBetting: false,
        showGameList: false,
        showVvipLevelUpModal: false,
        showVvipCashbackModal: false,
        showRulesBetFreeModal: false,
        showBetFreeModal: false,
        showBetConditionModal: false,
        showBetSuccessModal: false,
        showLuckyDrawCashbackModal: false,
        showWarningTooManyQrCodePay: false,
        dateBetMB: '',
        betType: 2,
        luckyDrawDateBetApi: '',
        userAuthPreference: 'login' as 'login' | 'register',
        configNotification: {
            show: false,
            image: '',
            title: '',
            subTitle: '',
            suggestion: '',
            cancelBtn: '',
            acceptBtn: '',
            titleClass: '',
            suggestionClass: '',
            buttonClass: '',
            preventClose: false,
            classCustom: '',
            buttonCancelClass: '',
            buttonAcceptClass: '',
            onCancel: null,
            onClose: null,
            onAccept: null,
            isHideCloseButton: false,
        },
        showClubWorldCupFinalModal: true,
        noShowAgainClubWorldCupFinalModal: false,
        clubWorldCupFinalModalCheckTime: null as number | null,
        showCheckedClubWorldCupFinalModal: false,
        showClubWorldCupFinalRuleModal: false,
        showUefaChampionModal: false,
        noShowAgainUefaChampionModal: false,
        showUefaChampionBannerModal: false,
        noShowAgainUefaChampionBannerModal: false,
        uefaChampionModalCheckTime: null as number | null,
        uefaChampionBannerModalCheckTime: null as number | null,
        showCheckedUEFAModal: false,
    }),
    actions: {
        setUserAuthPreference(preference: 'login' | 'register') {
            this.userAuthPreference = preference
        },
        showAuthModal() {
            if (this.userAuthPreference === 'register') {
                this.showRegisterModal = true
            } else {
                this.showLoginModal = true
            }
        },
        queueModal(modalId: string) {
            const modalOrder = [
                PROMOTION_POPUP_METHOD.LD_FIFA_CLUB_CASHBACK,
                PROMOTION_POPUP_METHOD.LD_FIFA_CLUB,
                PROMOTION_POPUP_METHOD.LD_BONG_VANG,
                PROMOTION_POPUP_METHOD.LD_VIP_CASHBACK,
                PROMOTION_POPUP_METHOD.LD_VIP_RANKING,
                PROMOTION_POPUP_METHOD.SVIP,
                PROMOTION_POPUP_METHOD.LIVE_CASINO_REFUND,
                PROMOTION_POPUP_METHOD.FREE_SPIN,
                PROMOTION_POPUP_METHOD.RACE_TOP,
                PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT,
                PROMOTION_POPUP_METHOD.INSURANCE_NOTIFY,
                PROMOTION_POPUP_METHOD.LUCKY_DRAW,
                PROMOTION_POPUP_METHOD.LD_CLUB_WORLD_CUP_FINAL,
            ]
            if (!this.modalQueue.includes(modalId)) {
                this.modalQueue.push(modalId)
            }
            this.modalQueue.sort(
                (x, y) => modalOrder.indexOf(x) - modalOrder.indexOf(y)
            )
            this.currentModal = this.modalQueue[0]
        },
        processQueue() {
            if (!this.currentModal && this.modalQueue.length > 0) {
                const nextModal = this.modalQueue[0]
                this.currentModal = nextModal
            }
        },
        handleModalClose() {
            this.currentModal = null
            this.modalQueue.shift()
            this.processQueue()
        },
        clearModalQueue() {
            this.modalQueue = []
        },
        reset() {
            this.showLoginModal = false
            this.showForgotPasswordModal = false
            this.showPromotionDetail = false
            this.showUpdateFullNameModal = false
        },
        resetConfigNotification() {
            this.configNotification = {
                show: false,
                image: '',
                title: '',
                subTitle: '',
                suggestion: '',
                cancelBtn: '',
                acceptBtn: '',
                titleClass: '',
                suggestionClass: '',
                buttonClass: '',
                preventClose: false,
                classCustom: '',
                buttonCancelClass: '',
                buttonAcceptClass: '',
                onCancel: null,
                onClose: null,
                onAccept: null,
            }
        },
        initShowCheckedUEFAModal() {
            if (process.client) {
                this.showCheckedUEFAModal = false
                localStorage.removeItem('showCheckedUEFAModal')
            }
        },
        setShowCheckedUEFAModal(value: boolean) {
            this.showCheckedUEFAModal = value
            if (process.client) {
                localStorage.setItem('showCheckedUEFAModal', JSON.stringify(value))
            }
        },
        initShowAgainUefaChampionModal() {
            if (process.client) {
                const savedValue = localStorage.getItem(
                    'noShowAgainUefaChampionModal'
                )
                const savedCheckTime = localStorage.getItem(
                    'uefaChampionModalCheckTime'
                )
                this.noShowAgainUefaChampionModal = savedValue
                    ? JSON.parse(savedValue)
                    : false
                this.uefaChampionModalCheckTime = savedCheckTime
                    ? parseInt(savedCheckTime)
                    : null
            }
        },
        setShowAgainUefaChampionModal(value: boolean) {
            this.noShowAgainUefaChampionModal = value
            if (process.client) {
                localStorage.setItem(
                    'noShowAgainUefaChampionModal',
                    JSON.stringify(value)
                )
                if (value) {
                    const checkTime = Date.now()
                    this.uefaChampionModalCheckTime = checkTime
                    localStorage.setItem(
                        'uefaChampionModalCheckTime',
                        checkTime.toString()
                    )
                } else {
                    this.uefaChampionModalCheckTime = null
                    localStorage.removeItem('uefaChampionModalCheckTime')
                }
            }
        },
        initShowAgainUefaChampionBannerModal() {
            if (process.client) {
                const savedValue = localStorage.getItem(
                    'noShowAgainUefaChampionBannerModal'
                )
                const savedCheckTime = localStorage.getItem(
                    'uefaChampionBannerModalCheckTime'
                )
                this.noShowAgainUefaChampionBannerModal = savedValue
                    ? JSON.parse(savedValue)
                    : false
                this.uefaChampionBannerModalCheckTime = savedCheckTime
                    ? parseInt(savedCheckTime)
                    : null
            }
        },
        setShowAgainUefaChampionBannerModal(value: boolean) {
            this.noShowAgainUefaChampionBannerModal = value
            if (process.client) {
                localStorage.setItem(
                    'noShowAgainUefaChampionBannerModal',
                    JSON.stringify(value)
                )
                if (value) {
                    const checkTime = Date.now()
                    this.uefaChampionBannerModalCheckTime = checkTime
                    localStorage.setItem(
                        'uefaChampionBannerModalCheckTime',
                        checkTime.toString()
                    )
                } else {
                    this.uefaChampionBannerModalCheckTime = null
                    localStorage.removeItem('uefaChampionBannerModalCheckTime')
                }
            }
        },
    },
    persist: {
        storage: persistedState.localStorage,
        paths: ['userAuthPreference'], // persist userAuthPreference
    },
})
