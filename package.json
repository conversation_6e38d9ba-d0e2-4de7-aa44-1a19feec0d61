{"name": "z03ld02", "private": true, "scripts": {"analyze": "npx nuxi analyze", "build": "nuxt build", "dev:start": "nuxt build && nuxt start", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint:js": "eslint --ext \".ts,.vue\" --ignore-path .gitignore .", "start": "nuxt build && pm2 start ecosystem.config.cjs", "lint:prettier": "prettier --check .", "lint": "npm run lint:js && npm run lint:prettier", "lintfix": "prettier --write --list-different . && npm run lint:js --fix", "convert:image": "node convert-and-compress-images.js", "build:image": "npm run convert:image && nuxt build", "dev:image": "npm run convert:image && nuxt dev"}, "devDependencies": {"@nuxt/devtools": "^1.3.9", "@nuxt/image": "^1.8.0", "@nuxtjs/device": "^3.1.0", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/sitemap": "^5.3.5", "@pinia-plugin-persistedstate/nuxt": "^1.1.1", "@pinia/nuxt": "^0.5.1", "@types/node": "^18", "@vueuse/core": "^10.1.2", "@vueuse/nuxt": "^9.13.0", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "autoprefixer": "^10.4.14", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-prettier": "^4.2.1", "nuxt": "^3.2.2", "nuxt-beastcss": "^1.0.3", "postcss": "^8.4.24", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "sass": "^1.77.6", "sass-loader": "^14.2.1", "vue-eslint-parser": "^9.3.1"}, "dependencies": {"@livechat/widget-vue": "^1.3.2", "@nuxt/scripts": "^0.10.5", "@nuxt/ui": "^2.17.0", "@nuxtjs/dotenv": "^1.4.1", "@nuxtjs/i18n": "8.0.0-rc.8", "@nuxtjs/robots": "^4.1.6", "@tailwindcss/line-clamp": "^0.4.4", "@vuepic/vue-datepicker": "^10.0.0", "@vueuse/router": "^10.11.0", "axios": "^1.7.4", "dayjs": "^1.11.9", "dotenv": "^16.3.1", "he": "^1.2.0", "isomorphic-dompurify": "^2.4.0", "jose": "^5.9.6", "jwt-simple": "^0.5.6", "nuxt-booster": "^3.1.6", "nuxt-cache-ssr": "^1.1.0", "nuxt-gtag": "^3.0.1", "nuxt-icons": "^3.2.1", "nuxt-lodash": "^2.4.1", "nuxt-swiper": "^1.2.2", "nuxt-vitalizer": "^0.10.0", "pinia": "^2.1.3", "pinia-plugin-persistedstate": "^3.1.0", "postcss": "^8.4.24", "qrcode": "1.5.3", "sharp": "^0.33.5", "socket.io-client": "^4.7.5", "sweetalert2": "^11.6.13", "swiper": "^10.0.4", "v-drag": "^3.0.9", "v-lazy-image": "^2.1.1", "vee-validate": "^4.9.6", "vite-plugin-compression": "^0.5.1", "vue-router": "4", "yup": "^1.2.0"}}