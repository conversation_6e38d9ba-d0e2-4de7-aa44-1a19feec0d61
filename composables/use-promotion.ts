import { ENDPOINT } from '~/constants/api-endpoint'
import { OK } from '~/constants/api-status'
import { PROMOTION_POPUP_METHOD } from '~/constants/common'
import type { ResParamsPromotionPopup } from '~/interfaces/promotion'
import { defineStore } from 'pinia'

export const usePromotion = defineStore('promotionStore', () => {
     const config = useRuntimeConfig()
    const CLUB_WC_CHAMPION_LEAGUE_METHOD = (config.public.CLUB_WC_CHAMPION_LEAGUE_METHOD as string) || ''
    const { t } = useI18n()
    const { $alertHtmlConfirm, $alert } = useNuxtApp()
    const useUserStoreInstance = useUserStore()
    const { user } = storeToRefs(useUserStoreInstance)
    const fetcher = useCustomFetch()
    const useModalStoreInstance = useModalStore()
    const { queueModal } = useModalStoreInstance
    const { showPromotionsCancel } = storeToRefs(useModalStoreInstance)
    const modalInsuranceAmountId = ref(null)
    const modalInsuranceNotifyId = ref(null)
    const modalSVipId = ref(null)
    const modalCasinoRefundId = ref<number | null>(null)
    const modalFreeSpinId = ref<number | null>(null)
    const modalRaceTopId = ref<number | null>(null)
    const insuranceAmount = ref(0)
    const eventStageReward = ref()
    const currentRoundReward = ref()
    const raceTopData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const casinoRefundData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const freeSpinData = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const LDvipRacking = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const LDvipCashback = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const fifaClubCashback = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const luckyDrawAward = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const luckyDrawAward1 = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const luckyDrawAward2 = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const uefaChampionDataPopup = ref<ResParamsPromotionPopup>(
        {} as ResParamsPromotionPopup
    )
    const closePopup = async (id: string) => {
        if (id) {
            try {
                const { data, error } = await fetcher.postPromotion(
                    `${ENDPOINT.PROMOTION_CLOSE_POPUP}/${id}`,
                    {}
                )

                if (error.value) {
                    throw error
                }

                return { data, error }
            } catch (error) {
                console.error(error)
            }
        }
    }

    const closeInsuranceAmount = async () => {
        if (modalInsuranceAmountId.value) {
            try {
                await closePopup(modalInsuranceAmountId.value)
            } catch (error) {
                console.error(error)
            } finally {
                modalInsuranceAmountId.value = null
            }
        }
    }

    const closeInsuranceNotify = async () => {
        if (modalInsuranceNotifyId.value) {
            try {
                await closePopup(modalInsuranceNotifyId.value)
            } catch (error) {
                console.error(error)
            } finally {
                modalInsuranceNotifyId.value = null
            }
        }
    }

    const closeSvipModal = async () => {
        if (modalSVipId.value) {
            try {
                await closePopup(modalSVipId.value)
            } catch (error) {
                console.error(error)
            } finally {
                modalSVipId.value = null
            }
        }
    }
    const closeCasinoRefundModal = async () => {
        if (modalCasinoRefundId.value) {
            try {
                await closePopup(`${modalCasinoRefundId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalCasinoRefundId.value = null
            }
        }
    }
    const closeFreeSpinModal = async () => {
        if (modalFreeSpinId.value) {
            try {
                await closePopup(`${modalFreeSpinId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalFreeSpinId.value = null
            }
        }
    }
    const closeRaceTopModal = async () => {
        if (modalRaceTopId.value) {
            try {
                await closePopup(`${modalRaceTopId.value}`)
            } catch (error) {
                console.error(error)
            } finally {
                modalRaceTopId.value = null
            }
        }
    }

    const getPromotionPopup = async () => {
        try {
            const { data, error } = await fetcher.getPromotion(
                ENDPOINT.PROMOTION_POPUP,
                {}
            )

            if (error.value) {
                throw error
            }

            const popupRes = data?.value?.data

            if (data?.value?.status === OK && !!popupRes.length) {
                const popupAmountStatus = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT
                )
                const popupNotifyStatus = popupRes.find(
                    (e: { method: string }) =>
                        e?.method === PROMOTION_POPUP_METHOD.INSURANCE_NOTIFY
                )
                casinoRefundData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method ===
                            PROMOTION_POPUP_METHOD.LIVE_CASINO_REFUND
                    ) || ({} as ResParamsPromotionPopup)
                freeSpinData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.FREE_SPIN
                    ) || ({} as ResParamsPromotionPopup)
                raceTopData.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.RACE_TOP
                    ) || ({} as ResParamsPromotionPopup)
                LDvipRacking.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.LD_VIP_RANKING
                    ) || ({} as ResParamsPromotionPopup)
                LDvipCashback.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.LD_VIP_CASHBACK
                    ) || ({} as ResParamsPromotionPopup)
                fifaClubCashback.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === PROMOTION_POPUP_METHOD.LD_FIFA_CLUB_CASHBACK
                    ) || ({} as ResParamsPromotionPopup)
                luckyDrawAward1.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === 'lucky_draw_1'
                    ) || ({} as ResParamsPromotionPopup)
                luckyDrawAward2.value =
                    popupRes.find(
                        (e: { method: string }) =>
                            e?.method === 'lucky_draw_2'
                    ) || ({} as ResParamsPromotionPopup)
                uefaChampionDataPopup.value = popupRes.find(
                        (e: { method: string }) =>
                            e?.method === CLUB_WC_CHAMPION_LEAGUE_METHOD
                    )
                // Show modal insurance amount
                if (popupAmountStatus && popupAmountStatus.is_show) {
                    modalInsuranceAmountId.value = popupAmountStatus.id
                    insuranceAmount.value = +popupAmountStatus.amount
                    queueModal(PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT)
                }
                // Show modal insurance notify
                if (popupNotifyStatus && popupNotifyStatus.is_show) {
                    modalInsuranceNotifyId.value = popupNotifyStatus.id
                    queueModal(PROMOTION_POPUP_METHOD.INSURANCE_NOTIFY)
                }

                // Show modal event race top
                if (raceTopData.value?.is_show && raceTopData.value?.ranking) {
                    modalRaceTopId.value = raceTopData.value?.id
                    queueModal(PROMOTION_POPUP_METHOD.RACE_TOP)
                }

                // Show modal casino refund
                if (casinoRefundData.value && casinoRefundData.value.is_show) {
                    modalCasinoRefundId.value = casinoRefundData.value.id
                    queueModal(PROMOTION_POPUP_METHOD.LIVE_CASINO_REFUND)
                }
                // Show modal free spin
                if (freeSpinData.value && freeSpinData.value.is_show) {
                    modalFreeSpinId.value = freeSpinData.value.id
                    queueModal(PROMOTION_POPUP_METHOD.FREE_SPIN)
                }
                // Show modal vip ranking
                if (LDvipRacking.value && LDvipRacking.value?.is_show) {
                    queueModal(PROMOTION_POPUP_METHOD.LD_VIP_RANKING)

                }
                 // Show modal vip amount
                 if (LDvipCashback.value && LDvipCashback.value?.is_show) {
                    queueModal(PROMOTION_POPUP_METHOD.LD_VIP_CASHBACK)
                }
                // Show modal fifa club amount
                if (fifaClubCashback.value && fifaClubCashback.value?.is_show) {
                    queueModal(PROMOTION_POPUP_METHOD.LD_FIFA_CLUB_CASHBACK)
                }
                // Show modal lucky draw award
                if (luckyDrawAward1.value && luckyDrawAward1.value?.is_show) {
                    luckyDrawAward.value = luckyDrawAward1.value
                    queueModal(PROMOTION_POPUP_METHOD.LUCKY_DRAW)
                }
                else if (luckyDrawAward2.value && luckyDrawAward2.value?.is_show) {
                    luckyDrawAward.value = luckyDrawAward2.value
                    queueModal(PROMOTION_POPUP_METHOD.LUCKY_DRAW)
                }
                if (uefaChampionDataPopup.value && uefaChampionDataPopup.value.is_show) {
                    queueModal(CLUB_WC_CHAMPION_LEAGUE_METHOD as string)
                }
            }

            return { data, error }
        } catch (error) {
            throw error
        }
    }

    const cancelPromotionStandard = async () => {
        $alertHtmlConfirm(
            t('modal.alert.title.notify'),
            t('modal.alert.content.confirm_cancel_promotion'),
            {}
        ).then(async (result) => {
            if (result.isDismissed) {
                return false
            }
            if (result.isConfirmed) {
                const { data } = await fetcher.post(
                    ENDPOINT.CANCEL_PROMOTION,
                    {}
                )
                if (data.value && data.value.status === OK) {
                    $alert(t('modal.alert.content.cancel_promotion_success'))
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                } else if (data.value && data.value.message) {
                    $alert(data.value.message)
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                    return false
                } else {
                    $alert(t('modal.alert.content.cancel_promotion_fail'))
                    setTimeout(() => {
                        window.location.reload()
                    }, 3000)
                    return false
                }
            }
        })
    }
    const cancelPromotion = async () => {
        if (user.value?.package_id && user.value.package_id !== 1) {
            $alertHtmlConfirm(
                t('modal.alert.title.notify'),
                t('modal.alert.content.confirm_cancel_promotion_checking'),
                {}
            ).then(async (result) => {
                if (result.isConfirmed) {
                    showPromotionsCancel.value = true
                }
            })
            return false
        }
        return true
    }

    const getDataStageReward = async () => {
        try {
            const { data, error } = await fetcher.getPromotion(
                ENDPOINT.STAGE_REWARD,
                {}
            )

            if (error.value) {
                throw error
            }

            if (data?.value?.status === OK && !!data?.value?.data) {
                eventStageReward.value = data?.value?.data
            }
        } catch (error) {
            throw error
        }
    }

    const getStageRewardByRoundId = (roundId: string) => {
        // currentRoundReward.value = eventStageReward.value[roundId]
        const reward = {
            id: roundId,
            ...eventStageReward.value[roundId],
        }
        currentRoundReward.value = reward
        return reward
    }
    const closeEventModal = async (modalId: string) => {
        try {
            await closePopup(modalId)
        } catch (error) {
            console.error(error)
        } finally {
            modalRaceTopId.value = null
        }
    }
    const closeUefaModal = async (id: string) => {
        if (id) {
            try {
                await closePopup(id)
            } catch (error) {
                console.error(error)
            }
        }
    }

    return {
        modalInsuranceAmountId,
        modalInsuranceNotifyId,
        modalSVipId,
        modalCasinoRefundId,
        insuranceAmount,
        casinoRefundData,
        eventStageReward,
        currentRoundReward,
        raceTopData,
        freeSpinData,
        closePopup,
        closeInsuranceAmount,
        closeInsuranceNotify,
        closeSvipModal,
        closeCasinoRefundModal,
        closeFreeSpinModal,
        closeRaceTopModal,
        getPromotionPopup,
        cancelPromotion,
        cancelPromotionStandard,
        getDataStageReward,
        getStageRewardByRoundId,
        LDvipRacking,
        LDvipCashback,
        closeEventModal,
        fifaClubCashback,
        luckyDrawAward,
        luckyDrawAward1,
        luckyDrawAward2,
        closeUefaModal,
        uefaChampionDataPopup,
    }
})
