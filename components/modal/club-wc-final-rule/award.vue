<template>
    <CommonModal @close="closeSelf" size="xxl" sticky>
        <template #default>
            <div class="aff-gift__body">
                <div class="mb-2">{{ $t('club_wc_final_rule.title') }}</div>
                <div
                    class="aff-gift__amount mb-2 flex h-[86px] items-center justify-center rounded-xl bg-white text-center font-black leading-[60px]"
                >
                    <span class="text-[24px]/[32px]">
                        {{
                            uefaChampionDataPopup?.amount_txt ||
                            NumberUtils.formatAmount(
                                uefaChampionDataPopup?.amount,
                                'VND'
                            )
                        }}
                    </span>
                </div>
                <div
                    class="whitespace-pre-line text-sm text-z-black-700"
                    v-html="$t('club_wc_final_rule.description', { brandName })"
                ></div>
                <div class="aff-gift__btn">
                    <span @click="closeSelf">{{
                        $t('club_wc_final_rule.close')
                    }}</span>
                </div>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { NumberUtils } from '~/utils'
const brandName = useRuntimeConfig().public.BRAND_NAME
const useModalStoreInstance = useModalStore()
const usePromotionInstance = usePromotion()
const { handleModalClose } = useModalStoreInstance
const { closeUefaModal } = usePromotionInstance
const { uefaChampionDataPopup } = storeToRefs(usePromotionInstance)
const closeSelf = async () => {
    await closeUefaModal(uefaChampionDataPopup.value?.id)
    handleModalClose()
}
</script>
<style lang="scss" scoped>
:deep(.modal-content) {
    @apply flex size-full lg:!h-[674px] flex-col justify-center py-5 lg:!w-[658px] lg:max-w-[658px];
    background: url('/assets/images/event-club-wc-final/bg-light.avif');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}
.aff-gift {
    &__body {
        background: url('/assets/images/event-club-wc-final/bg-gift.avif');
        background-size: contain;
        @apply mx-auto h-[320px] w-[340px] max-w-full px-[38.5px] pt-[120px] text-sm leading-[23px];
    }
    &__amount {
        span {
            @apply font-extrabold;
            background: linear-gradient(90deg, #303C79 0%, #011CA0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
    }
    &__btn {
        span {
            font-family: 'Be Vietnam Pro', sans-serif;
            @apply mx-auto mt-[12px] flex h-10 w-[164px] cursor-pointer items-center justify-center text-sm font-bold uppercase text-z-black-800;
            background: url('/assets/images/event-club-wc-final/pop-rule-btn.avif')
                center no-repeat;
            background-size: 100% 100%;
        }
    }
}
</style>
