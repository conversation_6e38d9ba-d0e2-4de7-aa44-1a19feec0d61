<template>
    <CommonModal
        :show="showClubWorldCupFinalRuleModal"
        @close="showClubWorldCupFinalRuleModal = false"
        aria-hidden="true"
        class-custom="modal-club-wc-final-rule animated fadeInUp"
        tabindex="-1"
        classContainer="!z-[113]"
    >
        <div class="club-wc-final-rule">
            <button
                aria-label="Close"
                class="close absolute right-4 top-3 xl:right-6 xl:top-[26px]"
                data-dismiss="modal"
                type="button"
                @click="showClubWorldCupFinalRuleModal = false"
            >
                <img
                    :src="`${staticUrl}/event-club-wc-final/close-no-bg.avif`"
                    alt="close"
                    class="close size-6 hover:opacity-70"
                />
            </button>

            <img
                :src="`${staticUrl}/event-club-wc-final/pop-rule-title.avif`"
                alt="club-wc-final-rule"
                class="absolute left-1/2 top-0 h-[3.125rem] w-[13.25rem] translate-x-[-50%] translate-y-[-50%] xl:h-[73px] xl:w-[309px]"
            />
            <div class="modal-body mt-3 xl:mt-[52px]">
                <div class="max-h-[514px] overflow-y-auto modal-body__scroll">
                    <div
                        class="mb-1 text-left text-sm font-extrabold uppercase text-z-black-800"
                    >
                        {{ t('club_wc_final_rule.rule') }}
                    </div>
                    <div class="rounded-lg bg-[#F2F3F4] p-[16px] min-h-[244px] lg:min-h-max lg:p-4 lg:pl-2.5">
                        <ul
                            class="flex flex-col items-start justify-start gap-1 text-left text-z-black-700"
                        >
                            <li
                                v-html="t('club_wc_final_rule.rule_content')"
                            ></li>
                            <li
                                v-html="t('club_wc_final_rule.rule_content_1')"
                            ></li>
                            <li
                                v-html="t('club_wc_final_rule.rule_content_2')"
                            ></li>
                            <li
                                v-html="t('club_wc_final_rule.rule_content_3')"
                            ></li>
                        </ul>
                    </div>

                    <div
                        class="mb-1 mt-4 text-left text-sm font-extrabold uppercase text-z-black-800"
                    >
                        {{ t('club_wc_final_rule.terms') }}
                    </div>
                    <div class="rounded-lg bg-[#F2F3F4] p-[16px] min-h-[352px] lg:min-h-max lg:p-4 lg:pl-2.5">
                        <ul
                            class="flex flex-col items-start justify-start gap-1 text-left text-z-black-700"
                        >
                            <li
                                v-html="
                                    t('club_wc_final_rule.terms_content', {
                                        brandName,
                                    })
                                "
                            />
                            <li
                                v-html="t('club_wc_final_rule.terms_content_1')"
                            />
                            <li
                                v-html="t('club_wc_final_rule.terms_content_2')"
                            />
                            <li
                                v-html="
                                    t('club_wc_final_rule.terms_content_3', {
                                        brandName,
                                    })
                                "
                            />
                            <li
                                v-html="
                                    t('club_wc_final_rule.terms_content_4', {
                                        brandName,
                                    })
                                "
                            />
                            <li
                                v-html="t('club_wc_final_rule.terms_content_5')"
                            />
                        </ul>
                    </div>
                </div>
                <div
                    @click="handleBetNow"
                    class="bg-btn-ct mx-auto mt-6 flex h-[44px] w-[181px] cursor-pointer items-center justify-center"
                >
                    <button class="uppercase">
                        {{ t('club_wc_final_rule.bet_now') }}
                    </button>
                </div>
            </div>
        </div>
    </CommonModal>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { PAGE_URL } from '~/constants/page-urls'
import { useWindowSize } from '~/composables/use-window'

const { staticUrl, BRAND_NAME: brandName } = useRuntimeConfig().public
const router = useRouter()
const useModalStoreInstance = useModalStore()
const { showClubWorldCupFinalRuleModal } = storeToRefs(useModalStoreInstance)
const { t } = useI18n()
const { isDeskTop } = useWindowSize()
const { openSport } = usePlayGame()
const handleBetNow = () => {
    if (isDeskTop.value) {
        router.push(PAGE_URL.K_SPORTS)
    } else {
        try {
            openSport({
                id: 'k-sport',
                title: 'K - SPORTS',
                type: 6,
                isRequireLogin: false,
                apiUrl: '/tp/ksportUrl',
                link: '/ksport',
            })
        } catch (error) {
            return
        }
    }
    setTimeout(() => {
        showClubWorldCupFinalRuleModal.value = false
    }, 200)
}
</script>

<style lang="scss" scoped>
:deep(.modal-dialog__wrap) {
    @apply p-0 items-end lg:items-center lg:p-2;
}
:deep(.modal-club-wc-final-rule) {
    @apply relative h-auto max-w-full mb-0 overflow-visible lg:max-w-[638px] rounded-b-none lg:rounded-b-2xl lg:mb-4;
    .modal-body__scroll {
        @media (max-width: 992px) {
            @apply max-h-[504px];
            &::-webkit-scrollbar {
                display: none;
            }
            & {
                -ms-overflow-style: none; /* IE and Edge */
                scrollbar-width: none; /* Firefox */
            }
        }
    }
}
.club-wc-final-rule {
    @apply flex w-full flex-col rounded-xl bg-white px-[16px] py-6 lg:p-6;
}
li {
    @apply relative leading-[20px] pl-[20px] text-[14px] lg:leading-5 lg:pl-5;
    &:before {
        @apply content-[''] bg-[#3c3c3c] absolute left-[7px] top-[7px] lg:top-[9px] h-[5px] w-[5px] rounded-full;
    }
}
.bg-btn-ct {
    background-image: url('/assets/images/event-club-wc-final/pop-rule-btn.avif');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    button {
        font-family: 'Be Vietnam Pro', sans-serif;
        height: 20px;
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        text-transform: uppercase;
        color: #17181a;
        text-align: center;
    }
}
</style>
