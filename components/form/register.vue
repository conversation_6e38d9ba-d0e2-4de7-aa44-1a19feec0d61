<template>
    <form @submit.prevent="handleSubmitForm" id="register">
        <div class="space-y-3">
            <CommonTextInput
                suffixIcon="popup/auth/user"
                v-model.trim="username"
                :placeholder="t('auth.plhd_username')"
                :error="errors.username"
                :errorMessage="usernameExist ? t('error.username_exist') : null"
                :maxlength="MAX_LENGTH.USERNAME - 1"
                isValidateSpace
                inputClass="h-11"
                class="text-input w-full"
                @focus="onFieldFocus"
            />
            <CommonTextInputPassword
                suffixIcon="popup/auth/password"
                v-model.trim="password"
                :placeholder="t('auth.plhd_password')"
                :error="errors.password"
                :maxlength="MAX_LENGTH.PASSWORD"
                inputClass="h-11 relative"
                class="text-input w-full"
                isValidateSpace
                isShowSuffixIcon
                @focus="onFieldFocus"
            />
            <CommonTextInputNumber
                suffixIcon="popup/auth/phone"
                v-model="phone"
                type="tel"
                :placeholder="t('auth.plhd_phone')"
                :error="inValidPhone || errors.phone"
                :maxlength="MAX_LENGTH.PHONE"
                inputClass="h-11"
                class="text-input w-full"
                @focus="onFieldFocus"
            />
            <div v-if="isV2Visible" :id="v2ContainerId" class="mt-4 recaptcha-v2-container"></div>
        </div>
        <div
            class="mx-auto mt-6 flex max-w-full justify-center lg:max-w-[15.063rem]"
        >
            <button
                type="submit"
                class="w-full rounded-md bg-[#D01C2D] p-3 text-base font-medium uppercase leading-snug text-white"
                :class="{
                    loading: isLoading,
                    'hover-btn': !isLoading && !usernameExist,
                    ' cursor-no-drop border border-solid bg-[#d01c2d66]':
                        isLoading || usernameExist,
                }"
                :disabled="isLoading || usernameExist"
            >
                {{ t('auth.register.button_register') }}
            </button>
        </div>
        <div class="form-custom-link mt-6 flex justify-center">
            <div type="button" class="mx-auto font-semibold text-[#1C1C1C]">
                {{ t('auth.register.text') }}
                <button @click="goToLogin" class="text-[#D01C2D]">
                    {{ t('auth.register.button') }}
                </button>
            </div>
        </div>
    </form>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useForm } from 'vee-validate'
import { useThrottleFn } from '@vueuse/core'
import { authForm } from '@/forms/auth.shema'
import { OK } from '~/constants/api-status'
import { PAGE_URL } from '~/constants/page-urls'
import { useUserStore } from '~/composables/use-user'
import { MAX_LENGTH, MIN_LENGTH, VALIDATE_RULE } from '~/constants'
import { useRecaptcha } from '~/composables/use-recaptcha'

const { executeRecaptcha, isV2Visible, v2ContainerId } = useRecaptcha()
const token = ref('')
const { t } = useI18n()
const router = useRouter()
const useUserStoreInstance = useUserStore()
const useModalStoreInstance = useModalStore()
const { showRegisterModal, showRegisterSuccessModal } = storeToRefs(
    useModalStoreInstance
)
const { alert } = useAlert()
const initialValues = {
    username: '',
    password: '',
    phone: '',
    token: '',
}

const isLoading = ref(false)

const validationSchema = authForm(t)
const { $toast } = useNuxtApp()
const { BRAND_NAME: brandName } = useRuntimeConfig().public
const { register, verifyUsername } = useUserStoreInstance
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const { handleSubmit, errors, defineField, resetForm } = useForm({
    initialValues,
    validationSchema,
})

const recaptchaTriggered = ref(false)

const [username] = defineField('username')
const [password] = defineField('password')
const [phone] = defineField('phone')
const usernameExist = ref(false)
const inValidPhone = ref('')

const onFieldFocus = () => {
    // Trigger reCAPTCHA v3 on first field focus
    if (!recaptchaTriggered.value) {
        recaptchaTriggered.value = true
        // Pre-load reCAPTCHA v3 silently
        executeRecaptcha('register').catch(error => {
            console.warn('reCAPTCHA pre-load failed:', error)
        })
    }
}

const handleSubmitForm = handleSubmit(async (payload) => {
    try {
        if (!!inValidPhone.value || usernameExist.value) {
            return
        }
        isLoading.value = true

        let recaptchaToken = ''
        try {
            recaptchaToken = await executeRecaptcha('register')
        } catch (recaptchaError) {
            console.warn('reCAPTCHA error, continuing without token:', recaptchaError)
        }
        const { data, error } = await register({ ...payload, token: recaptchaToken })

        if (error.value) {
            throw error
        }
        if (data.value && data.value.status === OK) {
            showRegisterModal.value = false
            showRegisterSuccessModal.value = true
            setTimeout(() => {
                showRegisterSuccessModal.value = false
                router.push(PAGE_URL.DEPOSIT_LINK.CODEPAY)
            }, 1400)
        }
        if (data.value && data.value.status !== OK) {
            alert(data?.value?.message)
            showRegisterModal.value = true
        }
    } catch (error) {
        alert(t('auth.register.error'))
    } finally {
        isLoading.value = false
    }
})

const handleVerifyUsername = useThrottleFn(async () => {
    if (!VALIDATE_RULE.REGISTER_USERNAME.test(username.value)) {
        usernameExist.value = false
        return
    }

    if (
        username.value.length < MIN_LENGTH.USERNAME ||
        username.value.length > MAX_LENGTH.USERNAME
    ) {
        usernameExist.value = false
        return
    }

    const { data, error } = await verifyUsername({ username: username.value })

    if (error.value) {
        $toast(error?.value?.data?.message, 'error')
    }
    usernameExist.value = data.value.exist
}, 100)
const handleVerifyPhone = (phoneNumber) => {
    if (!phoneNumber) {
        inValidPhone.value = ''
        return
    }
const phoneRegex = /^0(3[2-9]|5[2-9]|7[0|6-9]|8[1-9]|9[0-9])\d{7}$/;
    inValidPhone.value = !phoneRegex.test(phoneNumber)
        ? 'Số điện thoại không đúng định dạng'
        : ''
}
const goToLogin = () => {
    showRegisterModal.value = false
    recaptchaTriggered.value = false
    resetForm()
    usernameExist.value = false
    showLoginModal.value = true
    useModalStoreInstance.setUserAuthPreference('login')
}

watch(username, (newUsername) => {
    handleVerifyUsername(newUsername)
})
watch(phone, (newPhone) => {
    handleVerifyPhone(newPhone)
})
</script>
<style lang="scss" scoped>
:deep(.text-input .suffix-icon) {
    @apply absolute left-2 top-3 size-5 text-xl;
}
:deep(.text-input input) {
    @apply pl-8;
}
.recaptcha-v2-container {
    display: none;
    justify-content: center;
    align-items: center;
    min-height: 78px;
    margin: 10px 0;
    border: 1px dashed #ccc;
    padding: 10px;
    background: #f9f9f9;
}
</style>
