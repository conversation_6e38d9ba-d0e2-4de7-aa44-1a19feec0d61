<template>
    <form @submit="handleLogin">
            <div class="space-y-3">
                <CommonTextInput
                    suffixIcon="popup/auth/user"
                    v-model.trim="username"
                    :label="''"
                    :placeholder="t('auth.login.plhd_username')"
                    :error="errors.username"
                    :maxlength="MAX_LENGTH.USERNAME - 1"
                    inputClass="h-11"
                    isValidateSpace
                    class="w-full text-input"
                    @focus="onFieldFocus"
                />
                <CommonTextInputPassword
                    suffixIcon="popup/auth/password"
                    isShowSuffixIcon
                    v-model.trim="password"
                    :label="''"
                    :placeholder="t('auth.login.plhd_password')"
                    :error="errors.password || messageLoginFail"
                    :maxlength="MAX_LENGTH.PASSWORD"
                    isValidateSpace
                    inputClass="h-11 relative"
                    class="w-full text-input"
                    @focus="onFieldFocus"
                />
                <!-- reCAPTCHA v2 container (hidden by default) -->
                <div v-if="isV2Visible" :id="v2ContainerId" class="mt-4 recaptcha-v2-container"></div>
            </div>
            <div class="mt-4 mb-6 text-right font-semibold">
                <span
                    @click="clickForgotPassword"
                    class="inline-block cursor-pointer text-[#D01C2D] hover:underline"
                >
                    {{ $t('auth.forgot_password') }}
                </span>
            </div>
            <div class="flex justify-center mx-auto max-w-full lg:max-w-[15.063rem]">
                <button
                    type="submit"
                    class="w-full rounded-md bg-[#D01C2D] p-3 font-medium text-base uppercase leading-snug text-white"
                    :class="{ loading: isLoading ,'hover-btn': !isLoading}"
                    :disabled="isLoading"
                >
                    {{ $t('common.login') }}
                </button>
            </div>
            <div class="form-custom-link mt-6 flex justify-center">
                <div
                    class="mx-auto text-[#1C1C1C] font-semibold"
                >
                    {{ t('auth.login.register') }} <button @click="goToRegister" type="button" class="text-[#D01C2D]">{{ t('auth.login.register_new') }}</button>
                </div>
            </div>
        </form>
</template>


<script setup>
import { storeToRefs } from 'pinia'
import { useForm } from 'vee-validate'
import { loginForm } from '@/forms/auth.shema'
import { MAX_LENGTH } from '~/constants'
import { OK } from '~/constants/api-status'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import { useRecaptcha } from '~/composables/use-recaptcha'

const { executeRecaptcha, isV2Visible, v2ContainerId } = useRecaptcha()
const staticUrl = useRuntimeConfig().public.staticUrl
const { t } = useI18n()
const token = ref('')
const recaptchaTriggered = ref(false)

const useModalStoreInstance = useModalStore()
const { showLoginModal, showForgotPasswordModal, showRegisterModal } =
    storeToRefs(useModalStoreInstance)
const useUserInstances = useUserStore()
const { login } = useUserInstances
const isLoading = ref(false)

const initialValues = {
    username: '',
    password: '',
    token: '',
}

const validationSchema = loginForm(t)
const { handleSubmit, errors, defineField, resetForm } = useForm({
    initialValues,
    validationSchema,
})
const [username] = defineField('username')
const [password] = defineField('password')
const messageLoginFail = ref('')

const onFieldFocus = () => {
    // Trigger reCAPTCHA v3 on first field focus
    if (!recaptchaTriggered.value) {
        recaptchaTriggered.value = true
        // Pre-load reCAPTCHA v3 silently
        executeRecaptcha('login').catch(error => {
            console.warn('reCAPTCHA pre-load failed:', error)
        })
    }
}

const clickForgotPassword = () => {
    showLoginModal.value = false
    showForgotPasswordModal.value = true
}

const handleLogin = handleSubmit(async (payload) => {
    try {
        if (!payload.username || !payload.password) {
            messageLoginFail.value = t('error.login')
            return
        }
        isLoading.value = true

        let recaptchaToken = ''
        try {
            recaptchaToken = await executeRecaptcha('login')
        } catch (recaptchaError) {
            console.warn('reCAPTCHA error, continuing without token:', recaptchaError)
        }
        const { data } = await login({ ...payload, token: recaptchaToken})

        if (data.value && data.value.status === OK) {
            showLoginModal.value = false
        }
        if (data && data.status !== OK) {
            messageLoginFail.value = data.value.message
        }
    } catch (error) {
        console.log('error', error)
    } finally {
        isLoading.value = false
    }
})

const goToRegister = () => {
    showLoginModal.value = false
    showRegisterModal.value = true
    recaptchaTriggered.value = false
    resetForm()
    useModalStoreInstance.setUserAuthPreference('register')
}

watch(showLoginModal, (value) => {
    if (value) {
        messageLoginFail.value = ''
        resetForm()
        recaptchaTriggered.value = false
    }
})
</script>

<style lang="scss" scoped>
:deep(.text-input .suffix-icon) {
    @apply text-xl size-5 absolute left-2 top-3;
}
:deep(.text-input input) {
    @apply pl-8;
}

.recaptcha-v2-container {
    display: none;
    justify-content: center;
    align-items: center;
    min-height: 78px;
    margin: 10px 0;
    border: 1px dashed #ccc;
    padding: 10px;
    background: #f9f9f9;
}
</style>
