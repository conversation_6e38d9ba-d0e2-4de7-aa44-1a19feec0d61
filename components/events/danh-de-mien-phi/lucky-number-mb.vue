<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useEventDanhDe } from '~/composables/use-event-danh-de'
import type {
    LuckyDrawData,
    LuckyDrawDataInfo,
    BetFormValues,
} from '~/interfaces/event'
import { useForm } from 'vee-validate'
import { betFreeForm } from '~/forms/betting.schema'
import { computed } from 'vue'
const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { isLogged, user } = storeToRefs(useUserStoreInstance)
const {
    showLoginModal,
    showBetConditionModal,
    betType,
    dateBetMB,
    showWarningPromotion,
} = storeToRefs(useModalStoreInstance)
const { betLuckyDraw, getLuckyNumbers, getAmountLucky } = useEventDanhDe()
const { t } = useI18n()

const props = defineProps({
    luckyDraw: {
        type: Object as PropType<LuckyDrawData>,
        required: true,
    },
    luckyDrawInfo: {
        type: Object as PropType<LuckyDrawDataInfo>,
        required: true,
    },
    isLoadingInfo: {
        type: Boolean,
        default: false,
    },
    isLoadingLuckyDraw: {
        type: Boolean,
        default: false,
    },
})

const initialValuesTwoDigits = {
    number1: '',
    number2: '',
}

const initialValuesThreeDigits = {
    number3: '',
    number4: '',
    number5: '',
}

const validationSchemaTwoDigits = computed(() => betFreeForm(t, 2))
const validationSchemaThreeDigits = computed(() => betFreeForm(t, 3))

const {
    handleSubmit: handleSubmitTwoDigits,
    errors: errorsTwoDigits,
    defineField: defineFieldTwoDigits,
    resetForm: resetFormTwoDigits,
} = useForm({
    initialValues: initialValuesTwoDigits,
    validationSchema: validationSchemaTwoDigits,
})

const {
    handleSubmit: handleSubmitThreeDigits,
    errors: errorsThreeDigits,
    defineField: defineFieldThreeDigits,
    resetForm: resetFormThreeDigits,
} = useForm({
    initialValues: initialValuesThreeDigits,
    validationSchema: validationSchemaThreeDigits,
})

const [number1] = defineFieldTwoDigits('number1')
const [number2] = defineFieldTwoDigits('number2')
const [number3] = defineFieldThreeDigits('number3')
const [number4] = defineFieldThreeDigits('number4')
const [number5] = defineFieldThreeDigits('number5')

const onPasteBetNumber = (text: string, field: keyof BetFormValues): void => {
    if (!text.match(/[\d]/g)) {
        return
    }
    if (field === 'number1') number1.value = text.trim()
    if (field === 'number2') number2.value = text.trim()
    if (field === 'number3') number3.value = text.trim()
    if (field === 'number4') number4.value = text.trim()
    if (field === 'number5') number5.value = text.trim()
}
const firstTwoNumbers = computed(() => {
    return getLuckyNumbers(props.luckyDraw?.lucky_number_1?.toString())
})
const firstThreeNumbers = computed(() => {
    return getLuckyNumbers(props.luckyDraw?.lucky_number_2?.toString())
})

const amountLuckyNumber1 = computed(() => {
    return getAmountLucky(
        props.luckyDrawInfo.lucky_number_1_txt,
        props.luckyDrawInfo.lucky_number_1
    )
})

const amountLuckyNumber2 = computed(() => {
    return getAmountLucky(
        props.luckyDrawInfo.lucky_number_2_txt,
        props.luckyDrawInfo.lucky_number_2
    )
})

const hasErrorsTwoDigits = computed(
    () => Object.keys(errorsTwoDigits.value).length > 0
)
const hasErrorsThreeDigits = computed(
    () => Object.keys(errorsThreeDigits.value).length > 0
)

const isDisabled = computed(() => {
    if (hasErrorsTwoDigits.value) return true
    return !(number1.value?.length === 2 && number2.value?.length === 2)
})

const isDisabledThree = computed(() => {
    if (hasErrorsThreeDigits.value) return true
    return !(
        number3.value?.length === 3 &&
        number4.value?.length === 3 &&
        number5.value?.length === 3
    )
})

const handleBet = async (type: number) => {
    if (isLogged.value) {
        if (user.value?.package_id === false || user.value?.package_id === 1) {
            betType.value = type
            if (type === 2 && !props.luckyDraw?.lucky_number_1) {
                showBetConditionModal.value = true
                return
            }
            if (type === 3 && !props.luckyDraw?.lucky_number_2) {
                showBetConditionModal.value = true
                return
            }
            try {
                if (type === 2) {
                    const numbers = `${number1.value},${number2.value}`
                    await betLuckyDraw(numbers, '')
                    resetFormTwoDigits()
                } else {
                    const numbers = `${number3.value},${number4.value},${number5.value}`
                    await betLuckyDraw('', numbers)
                    resetFormThreeDigits()
                }
            } catch (error) {
                console.error('Error betting:', error)
            }
            return
        } else {
            showWarningPromotion.value = true
        }
    } else {
        showLoginModal.value = true
    }
}
</script>
<template>
    <div class="no-scrollbar -mx-[0.938rem] overflow-x-auto">
        <div class="lucky-number-mb">
            <form @submit.prevent="handleBet(2)" class="lucky-number-mb__item">
                <div
                    v-if="!isLoadingInfo && !isLoadingLuckyDraw"
                    class="absolute left-0 top-0 flex size-full items-center justify-center"
                >
                    <img
                        width="50"
                        height="50"
                        :src="`${staticUrl}/loading.gif`"
                        alt="loading icon"
                    />
                </div>
                <template v-else>
                    <div
                        class="lucky-number-mb__title font-svn-vt pt-2 text-2xl leading-6 tracking-[0%]"
                    >
                        đánh ĐỀ 2 SỐ
                    </div>
                    <div
                        v-if="typeof luckyDraw?.lucky_number_1 !== 'string'"
                        class="relative"
                    >
                        <label
                            class="mb-1 mt-5 block text-sm font-medium leading-5 tracking-[0%] text-[#3C3C3C]"
                            >Đánh đề 2 số (00 - 99)</label
                        >
                        <div class="flex items-center gap-[8px] pr-[28px]">
                            <CommonTextInputNumber
                                v-model="number1"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!rounded-lg [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-[#BBBEC0]"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="2"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'number1')"
                            />
                            <CommonTextInputNumber
                                v-model="number2"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!rounded-lg [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-[#BBBEC0]"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="2"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'number2')"
                            />
                        </div>
                        <div
                            class="error-message mt-[4px] h-5 text-[14px] leading-[20px] text-[#FF0000]"
                            :class="
                                errorsTwoDigits.number1 ||
                                errorsTwoDigits.number2
                                    ? 'opacity-100'
                                    : 'opacity-0'
                            "
                        >
                            {{
                                errorsTwoDigits.number1 ||
                                errorsTwoDigits.number2
                            }}
                        </div>
                        <div class="icon-input absolute right-0 top-9">
                            <img
                                :src="`${staticUrl}/danh-de-mien-phi/lucky-number/tooltip.svg`"
                                alt="tooltip"
                                class="size-[1.25rem]"
                            />
                        </div>
                        <div
                            class="box-tooltip absolute -right-3 bottom-[60px] hidden w-[208px] rounded-[4px] bg-[#1C1C1C] p-[4px] py-[3px] text-[12px] font-semibold leading-[120%] text-white"
                        >
                            <ul>
                                <li>Chọn 1 số từ 00 đến 99.</li>
                                <li>So sánh với 2 số cuối giải ĐB miền Bắc.</li>
                                <li>
                                    Không thể chọn lại nếu đã bấm “Xác nhận”.
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div v-else class="mt-6">
                        <div class="flex items-center justify-between">
                            <div
                                class="leading-12 text-base font-semibold text-[#1C1C1C]"
                            >
                                Số đã chọn
                            </div>
                            <div
                                class="lucky-number-mb__bet leading-12 font-svn-vt flex gap-x-4 text-center text-2xl font-normal"
                            >
                                <span
                                    v-for="(number, index) in firstTwoNumbers"
                                    :key="index"
                                    >{{ number }}</span
                                >
                            </div>
                        </div>
                        <div
                            class="mb-3 mt-2.5 border-b border-solid border-[#EEE2CB] pb-[3.125rem] text-sm font-medium leading-5 text-[#906411]"
                        >
                            Đối chiếu kết quả với 2 số cuối giải<br />Đặc biệt
                            XSMB
                        </div>
                    </div>
                    <div class="mt-1">
                        <label
                            class="mb-1 block text-sm font-medium leading-5 tracking-[0%] text-[#3C3C3C]"
                            >Xổ số miền Bắc</label
                        >
                        <div
                            class="flex h-11 items-center rounded-lg border border-solid border-[#FBEBD3] bg-[#F5F6F7] px-3 text-sm font-normal leading-5 text-[#595A60]"
                        >
                            {{ dateBetMB }}
                        </div>
                    </div>
                    <div
                        v-if="typeof luckyDraw?.lucky_number_1 !== 'string'"
                        @click="handleBet(2)"
                        class="lucky-number-mb__btn mt-4"
                        :class="
                            isDisabled
                                ? 'pointer-events-none grayscale-[1]'
                                : ''
                        "
                    >
                        <span>XÁC NHẬN</span>
                    </div>
                    <div
                        class="absolute bottom-2.5 left-0 flex w-full items-center justify-between px-4"
                        :class="amountLuckyNumber1 == 0 ? 'opacity-0' : ''"
                    >
                        <div
                            class="text-xs font-extrabold leading-[130%] text-white"
                        >
                            QUỸ THƯỞNG
                        </div>
                        <div class="lucky-number-mb__money">
                            <span
                                :class="
                                    amountLuckyNumber1.length > 10
                                        ? '!text-lg'
                                        : ''
                                "
                                >{{ amountLuckyNumber1 }} VND</span
                            >
                        </div>
                    </div>
                </template>
            </form>
            <form @submit.prevent="handleBet(3)" class="lucky-number-mb__item">
                <div
                    v-if="!isLoadingInfo && !isLoadingLuckyDraw"
                    class="absolute left-0 top-0 flex size-full items-center justify-center"
                >
                    <img
                        width="50"
                        height="50"
                        :src="`${staticUrl}/loading.gif`"
                        alt="loading icon"
                    />
                </div>
                <template v-else>
                    <div
                        class="lucky-number-mb__title font-svn-vt pt-2 text-2xl leading-6 tracking-[0%]"
                    >
                        đánh ĐỀ 3 SỐ
                    </div>
                    <div
                        v-if="typeof luckyDraw?.lucky_number_2 !== 'string'"
                        class="relative"
                    >
                        <label
                            class="mb-1 mt-5 block text-sm font-medium leading-5 tracking-[0%] text-[#3C3C3C]"
                            >Đánh đề 3 số (000 - 999)</label
                        >
                        <div class="flex items-center gap-[8px] pr-[28px]">
                            <CommonTextInputNumber
                                v-model="number3"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!rounded-lg [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-[#BBBEC0]"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="3"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'number3')"
                            />
                            <CommonTextInputNumber
                                v-model="number4"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!rounded-lg [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-[#BBBEC0]"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="3"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'number4')"
                            />
                            <CommonTextInputNumber
                                v-model="number5"
                                placeholder="Nhập số"
                                inputClass="h-11 [&_input]:!border-[#FBEBD3] [&_input]:!rounded-lg [&_input]:!text-[#1C1C1C] [&_input]:placeholder:!text-[#BBBEC0]"
                                class="w-full"
                                inputmode="numeric"
                                type="tel"
                                :maxLength="3"
                                @onPaste="(text: string) => onPasteBetNumber(text, 'number5')"
                            />
                        </div>
                        <div
                            class="error-message mt-[4px] h-5 text-[14px] leading-[20px] text-[#FF0000]"
                            :class="
                                errorsThreeDigits.number3 ||
                                errorsThreeDigits.number4 ||
                                errorsThreeDigits.number5
                                    ? 'opacity-100'
                                    : 'opacity-0'
                            "
                        >
                            {{
                                errorsThreeDigits.number3 ||
                                errorsThreeDigits.number4 ||
                                errorsThreeDigits.number5
                            }}
                        </div>
                        <div class="icon-input absolute right-0 top-9">
                            <img
                                :src="`${staticUrl}/danh-de-mien-phi/lucky-number/tooltip.svg`"
                                alt="tooltip"
                                class="size-[1.25rem]"
                            />
                        </div>
                        <div
                            class="box-tooltip absolute -right-3 bottom-[60px] hidden w-[208px] rounded-[4px] bg-[#1C1C1C] p-[4px] py-[3px] text-[12px] font-semibold leading-[120%] text-white"
                        >
                            <ul>
                                <li>Chọn 1 số từ 000 đến 999.</li>
                                <li>So sánh với 3 số cuối giải ĐB miền Bắc.</li>
                                <li>
                                    Không thể chọn lại nếu đã bấm “Xác nhận”.
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div v-else class="mt-6">
                        <div class="flex items-center justify-between">
                            <div
                                class="leading-12 text-base font-semibold text-[#1C1C1C]"
                            >
                                Số đã chọn
                            </div>
                            <div
                                class="lucky-number-mb__bet leading-12 font-svn-vt flex gap-x-4 text-center text-2xl font-normal"
                            >
                                <span
                                    v-for="(number, index) in firstThreeNumbers"
                                    :key="index"
                                    >{{ number }}</span
                                >
                            </div>
                        </div>
                        <div
                            class="mb-3 mt-2.5 border-b border-solid border-[#EEE2CB] pb-[3.125rem] text-sm font-medium leading-5 text-[#906411]"
                        >
                            Đối chiếu kết quả với 3 số cuối giải<br />Đặc biệt
                            XSMB
                        </div>
                    </div>
                    <div class="mt-1">
                        <label
                            class="mb-1 block text-sm font-medium leading-5 tracking-[0%] text-[#3C3C3C]"
                            >Xổ số miền Bắc</label
                        >
                        <div
                            class="flex h-11 items-center rounded-lg border border-solid border-[#FBEBD3] bg-[#F5F6F7] px-3 text-sm font-normal leading-5 text-[#595A60]"
                        >
                            {{ dateBetMB }}
                        </div>
                    </div>
                    <div
                        v-if="typeof luckyDraw?.lucky_number_2 !== 'string'"
                        @click="handleBet(3)"
                        class="lucky-number-mb__btn mt-4"
                        :class="
                            isDisabledThree
                                ? 'pointer-events-none grayscale-[1]'
                                : ''
                        "
                    >
                        <span>XÁC NHẬN</span>
                    </div>
                    <div
                        class="absolute bottom-2.5 left-0 flex w-full items-center justify-between px-4"
                        :class="amountLuckyNumber2 == 0 ? 'opacity-0' : ''"
                    >
                        <div
                            class="text-xs font-extrabold leading-[130%] text-white"
                        >
                            QUỸ THƯỞNG
                        </div>
                        <div class="lucky-number-mb__money">
                            <span
                                :class="
                                    amountLuckyNumber2.length > 10
                                        ? '!text-lg'
                                        : ''
                                "
                                >{{ amountLuckyNumber2 }} VND</span
                            >
                        </div>
                    </div>
                </template>
            </form>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.lucky-number-mb {
    @apply mb-8 flex w-max gap-x-4 px-[0.938rem];
    &__item {
        @apply relative h-[20.75rem] w-[17.6875rem] px-6 last:w-[18.1875rem] last:px-[1.5625rem];
        background: url('/assets/images/danh-de-mien-phi/lucky-number/bg-lucky-number-mb.png')
            no-repeat center / 100% 100%;
        .icon-input {
            &:hover {
                & + .box-tooltip {
                    @apply block;
                }
            }
        }
        :deep(input) {
            &:focus {
                @apply shadow-[0px_0px_0px_2px_#F46E49];
            }
        }
    }
    &__title {
        background: linear-gradient(
            180deg,
            #cf3704 33.65%,
            #ad2106 70.08%,
            #cf1f04 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    &__btn {
        @apply mx-auto flex h-9 w-[11.25rem] items-center justify-center text-[0.9375rem] font-black;
        background: url('/assets/images/danh-de-mien-phi/banner/btn-mb.png')
            no-repeat center / 100% 100%;
        span {
            background: linear-gradient(180deg, #ea462e 0%, #c7310c 100%),
                linear-gradient(180deg, #cf2604 33.65%, #a50e03 70.08%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    &__money {
        span {
            @apply text-xl font-black leading-[100%];
            background: linear-gradient(
                338.45deg,
                #ffe57b 17.69%,
                #ffffff 65.98%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    &__bet {
        span {
            background: linear-gradient(
                180deg,
                #cf3704 33.65%,
                #ad2106 70.08%,
                #cf1f04 100%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
.box-tooltip {
    @media (max-width: 425px) {
        @apply py-1.5 text-[0.65rem];
    }
    ul {
        @apply pl-[20px];
        li {
            @apply list-disc;
        }
    }
    &::before {
        @apply absolute -bottom-[4px] right-[17px] h-0 w-0 border-x-[5px] border-b-0 border-t-4 border-solid border-x-transparent border-t-[#1C1C1C] content-[''];
    }
}
</style>
