<template>
    <li class="main-nav-item relative">
        <nuxt-link
            :to="generateHref(item)"
            :class="[
                'menu-item relative flex items-center font-semibold uppercase text-white',
                { 'has-submenu': item?.subMenu },
                { active: activeMenu },
            ]"
            @click.prevent="handleRedirection(item)"
        >
            {{ $t(`menu.${item.i18Key}`) }}
            <div v-if="item.label" class="label font-normal">
                <span>{{ item.label }}</span>
            </div>
        </nuxt-link>
        <div
            class="sub-menu absolute left-0 right-0 top-[100%] opacity-0 transition-all ease-in-out"
            v-if="item?.subMenu"
        >
            <ul class="sub-menu__content">
                <li
                    v-for="(subItem, index) in item.subMenu"
                    :key="index"
                    class="sub-menu__item"
                >
                    <a
                        :href="subItem.link"
                        :class="[{ active: $route.path === subItem.link }]"
                        @click.stop.prevent="handleRedirection(subItem)"
                    >
                        <nuxt-icon
                            class="icon-"
                            :name="subItem.icon"
                        ></nuxt-icon>
                        <span
                            class="truncate text-center text-sm font-semibold capitalize"
                        >
                            {{ $t(`menu.${subItem.i18Key}`) }}
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </li>
</template>

<script setup lang="ts">
import { type IMenu } from '~/resources/header.ts'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import { IFRAME_ENDPOINT } from '~/constants/api-endpoint'
import { PAGE_URL } from '~/constants/page-urls'

const staticUrl = useRuntimeConfig().public.staticUrl
const useUserStoreInstance = useUserStore()
const { user, isLogged } = storeToRefs(useUserStoreInstance)
const useModalStoreInstance = useModalStore()
const { showLoginModal, showWarningPromotion, showUpdateFullNameModal } = storeToRefs(
    useModalStoreInstance
)
const props = defineProps({
    item: {
        type: Object as PropType<IMenu>,
        default: () => {},
    },
})
const router = useRouter()
const route = useRoute()
const { openLink, playGameByApiUrl } = usePlayGame()

const siteUrl = useRuntimeConfig().public.SITE_URL
const { playGame, hasPromotion } = usePlayGame()

const subMenuLink = [
    '/cong-game/nohu',
    '/cong-game/lottery',
    '/cong-game/keno',
    '/cong-game/fishing',
    '/cong-game/game-bai',
]
const congGameActiveLink = [
    '/cong-game/all',
    '/cong-game/favorite',
    '/cong-game/xo-so',
    '/cong-game/other',
    '/cong-game/da-ga',
    '/cong-game/tables',
    '/cong-game/slots',
    '/cong-game/instant',
]
const promotionSubMenu = [
    '/event',
    '/sieu-hoan-tra-casino',
    '/the-loai/khuyen-mai/hoan-tra-vo-tan-10',
    '/the-loai/khuyen-mai/thuong-nap-100-len-den-15-trieu-dong',
    '/the-loai/khuyen-mai/thuong-nap-50-len-den-8-trieu-dong',
]
const handleRedirection = (item: IMenu) => {
    if (user.value && 'is_updated_fullname' in user.value && !user.value.is_updated_fullname && item?.checkFullName) {
        showUpdateFullNameModal.value = true
        return
    }
    const linkBlockWithPromotion = ['/spribe-keno', '/locphat-keno']
    if (linkBlockWithPromotion.includes(item.link) && hasPromotion()) {
        showWarningPromotion.value = true
        return
    }
    if (item.noRedirect) {
        return
    }
    if (item.loginRequired && !isLogged.value) {
        useModalStoreInstance.showAuthModal()
        return
    }
    if (item.isCockfight) {
        playGame({
            p: item?.partner || '',
            gId: 'lobby',
        })
        return
    }

    if (item.newTab) {
        const gameUrl = useRuntimeConfig().public.GAME_URL
        openLink(`${gameUrl}?url=${item.link}`, {
            loginRequired: item.loginRequired,
            newTab: true,
        })
        return
    }
    if (!item.link) {
        return
    }
    router.push(item.link as string)
}
const generateHref = (item: IMenu) => {
    let link = item.link as string

    if (link && !link.startsWith('/')) {
        link = '/' + link
    }
    return (siteUrl + link) as string
}
const activeMenu = computed(() => {
    return (
        (!subMenuLink.includes(route.path) &&
            route.path === props.item.link) ||
        route.path === props.item.link ||
        (props.item.subMenu?.some(
            (sub: string) => sub.link === route.path
        ) &&
            !subMenuLink.includes(route.path)) ||
        (route.name === 'livecasino-type' &&
            route.matched?.[0]?.path === props.item.link) ||
        (route.name === 'cong-game-type' &&
            congGameActiveLink.includes(route.path) &&
            props.item.link === `${PAGE_URL.GAME}`) ||
        (promotionSubMenu.includes(route.path) &&
            props.item.link === PAGE_URL.PROMOTIONS)
    )
})
const isMenuVisible = ref(false)
const toggleMenu = (isShow: boolean) => {
    isMenuVisible.value = isShow
}
</script>
