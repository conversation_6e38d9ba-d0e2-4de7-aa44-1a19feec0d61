<template>
    <div
        :class="[
            'header-user ms-auto flex items-center justify-items-center',
            { 'lg:absolute': (user?.username?.length ?? 0) > 15 },
        ]"
    >
        <div ref="notificationRef" class="notification-content relative flex">
            <button class="notification-icon" @click="handleOpenNotification">
                <nuxt-icon
                    filled
                    :name="isAllAsRead ? 'notify' : 'notify-active'"
                />
            </button>
            <div class="notification-wrapper" v-show="isOpenNotification">
                <div
                    class="absolute right-0 top-[2.375rem] w-[26.25rem] transition-all duration-[0.2s]"
                >
                    <Notification :notificationList="notificationList" />
                </div>
            </div>
        </div>
        <!-- <nuxt-link to="/user" class="header-user-icon block lg:hidden">
            <nuxt-icon filled name="user-mb" />
        </nuxt-link> -->
        <div class="header-user-info">
            <nuxt-link to="/user" class="header-user-info__icon">
                <nuxt-icon name="user" />
            </nuxt-link>
            <div class="header-user-info__content">
                <span class="user-name hidden lg:block lg:max-w-[5.625rem] truncate">{{
                    user?.fullname
                }}</span>
                <span class="amount font-bold lg:font-normal">{{
                    NumberUtils.formatAmount(user?.balance ?? 0)
                }}</span>
            </div>
            <nuxt-link to="/user/deposit" class="icon-add">
                <nuxt-icon filled name="add" />
            </nuxt-link>
        </div>
        <!-- End Notification -->
    </div>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { NumberUtils } from '~/utils'
import { useUserStore } from '~/composables/use-user'

const { isDeskTop } = useWindowSize()
const router = useRouter()

const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const socketStoreInstance = useSocket()
const { notificationList, isAllAsRead } = storeToRefs(socketStoreInstance)

const isOpenNotification = ref(false)
const notificationRef = ref<any>()

const handleOpenNotification = () => {
    if (isDeskTop.value) {
        isOpenNotification.value = !isOpenNotification.value
    } else {
        router.push('/notification')
    }
}

const closeNotificationBox = () => {
    isOpenNotification.value = false
}

onClickOutside(notificationRef, () => {
    closeNotificationBox()
})
</script>
