<template>
    <div class="header-btn-group flex items-center">
        <button
            type="button"
            class="header-btn btn-login hover-btn outline-none"
            @click="handleShowModalLogin"
        >
            {{ $t('header.login') }}
        </button>
        <button
            class="header-btn btn-signup hover-btn outline-none"
            @click="handleShowModalRegister"
        >
            {{ $t('header.register') }}
        </button>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { OK } from '~/constants/api-status'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/composables/use-user'
import { DISABLE_USER_MODAL } from '~/resources/header'

const { t } = useI18n()
const { alert } = useAlert()

const useModalStoreInstance = useModalStore()
const { showLoginModal, showLoginFailModal, showRegisterModal } = storeToRefs(
    useModalStoreInstance
)
const useUserInstances = useUserStore()
const { login } = useUserInstances
const isLoading = ref(false)
const router = useRouter()
const route = useRoute()
const formData = {
    username: '',
    password: '',
}

const handleLogin = async () => {
    try {
        if (!formData.username || !formData.password) {
            setTimeout(() => {
                alert(t('error.login'))
            })
            return
        }
        if (isLoading.value) {
            return
        }
        isLoading.value = true
        const { data } = await login(formData)

        if (data?.value && data.value.status === OK) {
            showLoginModal.value = false
        }
        if (data?.value && data.value.status !== OK) {
            showLoginFailModal.value = true
        }
    } catch (error) {
        alert(t('error.maintain'))
        console.log('error', error)
    } finally {
        isLoading.value = false
    }
}

const disableUserModal = computed(() =>
    DISABLE_USER_MODAL.some((el) => el === route.name)
)

const handleShowModalLogin = () => {
    // Set preference to login when clicking login button in header
    useModalStoreInstance.setUserAuthPreference('login')
    
    if (disableUserModal.value) {
        router.push('/login')
        showLoginModal.value = false
    } else {
        showLoginModal.value = true
    }
}

const handleShowModalRegister = () => {
    // Set preference to register when clicking register button in header
    useModalStoreInstance.setUserAuthPreference('register')
    
    if (disableUserModal.value) {
        router.push('/register')
        showRegisterModal.value = false
    } else {
        showRegisterModal.value = true
    }
}
</script>
