<template>
    <div :class="['menu-mobile fixed inset-0 z-[101]', { active: isShow }]">
        <div
            class="overlay-menu absolute inset-0"
            @click="$emit('close')"
        ></div>
        <div
            class="hide-scroll relative z-10 max-w-[85%] overflow-auto bg-white pb-14"
            :style="{ height: `${innerHeight}px` }"
        >
            <div
                class="sticky top-0 z-10 flex items-center justify-between bg-z-red-dit px-4 py-3"
            >
                <div @click="goTo('/')">
                    <img
                        :src="`${staticUrl}/${brandName.toLocaleLowerCase()}-logo.svg`"
                        alt="Logo"
                        class="m-auto h-9 w-[6.563rem]"
                        loading="lazy"
                    />
                </div>

                <div class="flex items-center">
                    <button class="p-1" @click="$emit('close')">
                        <img
                            class="size-6"
                            :src="`${staticUrl}/aside-bar/close.svg`"
                            alt="Logo"
                        />
                    </button>
                </div>
            </div>
            <div class="bg-[#F6F7FA] px-4 py-4">
                <a
                    :href="PROMOTION_TELEGRAM_LINK"
                    target="_blank"
                    class="flex items-center gap-3 rounded-lg border border-[#EAEFF7] bg-white p-3"
                >
                    <img
                        :src="`${staticUrl}/aside-bar/telegram-rounder.svg`"
                        alt="telegram"
                        class="size-[1.875rem] flex-shrink-0"
                    />
                    <div class="text-[0.8125rem] leading-[1.2553]">
                        <div class="title text-[#808080]">Đăng ký kênh</div>
                        <div class="mt-1 font-bold text-[#000]">Telegram</div>
                    </div>
                </a>
            </div>
            <div v-if="isShow" class="p-4">
                <LayoutMenuMobileItem
                    v-for="(item, index) in menuMobile"
                    :item="item"
                    :key="index"
                    @move-to="handleRedirection"
                />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { IFRAME_ENDPOINT } from '~/constants/api-endpoint'
import { type IMenu, menuMobile } from '~/resources/header'
import { useModalStore } from '~/stores'
const brandName = useRuntimeConfig().public.BRAND_NAME
const { PROMOTION_TELEGRAM_LINK } = useRuntimeConfig().public
const props = defineProps({
    isShow: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['close'])

const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const { showLoginModal, showUpdateFullNameModal } = storeToRefs(useModalStoreInstance)
const useUserStoreInstance = useUserStore()
const { isLogged, user } = storeToRefs(useUserStoreInstance)
const { openLink, playGameByApiUrl } = usePlayGame()
const { logout } = useUserStore()
const handleLogout = async () => {
    emit('close')
    await logout()
}
const { innerHeight } = useWindowSize()
const router = useRouter()
const goTo = (link: string) => {
    emit('close')
    router.push(link)
}

const handleRedirection = (item: IMenu) => {
    const apiLink = [
        'quay-so-1',
        'quay-so-2',
        'quay-so-5',
        'quay-so-3',
        'numbergame',
        'numbergame2',
        'keno',
        'vietlott-keno',
        'locphat-keno',
        'saba-keno',
        'spribe-keno',
        'lo-de-sieu-toc',
    ]
    if (item.loginRequired) {
        if (!isLogged.value) {
            emit('close')
            useModalStoreInstance.showAuthModal()
        } else if (apiLink.includes(item.link as string)) {
            playGameByApiUrl(`${IFRAME_ENDPOINT[item.link as string].apiUrl}`)
        } else {
            router.push(item.link as string)
            emit('close')
        }
        return
    }
    if (item.newTab) {
        if (apiLink.includes(item.link as string)) {
            playGameByApiUrl(`${IFRAME_ENDPOINT[item.link as string].apiUrl}`)
            return
        }
        const gameUrl = useRuntimeConfig().public.GAME_URL
        openLink(`${gameUrl}?url=${item.link}`, {
            loginRequired: item.loginRequired,
            newTab: true,
        })
        return
    }
    if (user.value && 'is_updated_fullname' in user.value && !user.value.is_updated_fullname && item?.checkFullName) {
        showUpdateFullNameModal.value = true
        return
    }
    emit('close')
    router.push(item.link as string)
}
watch(
    () => props.isShow,
    (newVal) => {
        if (newVal) {
            document.body.classList.add('modal-open')
        } else {
            document.body.classList.remove('modal-open')
        }
    }
)
</script>
<style lang="scss" scoped>
.bg-gradient1 {
    background: linear-gradient(230.92deg, #252020 -5.17%, #252121 109.31%);
}
.bg-gradient {
    background: linear-gradient(
        325.4deg,
        hsla(0, 0%, 100%, 0.024) 20.09%,
        rgba(41, 42, 44, 0.024) 96.67%
    );
}
.overlay-menu {
    background: rgba(0, 0, 0, 0.6);
}
.menu-mobile {
    visibility: hidden;
    transform: translateX(-100%);
    opacity: 0;
    transition: 0.3s;
    &.active {
        visibility: visible;
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
