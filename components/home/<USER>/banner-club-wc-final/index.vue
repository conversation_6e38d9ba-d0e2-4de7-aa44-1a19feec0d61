<template>
    <div class="banner-event" @click="handleClick">
        <CommonImage
            class="size-full"
            :max="`(max-width: 991px)`"
            :src="`${staticUrl}/home/<USER>/club-wc-final/pc.avif`"
            :srcMb="`${staticUrl}/home/<USER>/club-wc-final/mb.avif`"
            alt="banner"
            :width="isMobile ? 430 : 'auto'"
            :height="isMobile ? 180 : 'auto'"
        />
    </div>
</template>

<script setup lang="ts">
import { useModalStore } from '~/stores'
import { storeToRefs } from 'pinia'

const { isMobile } = useDevice()
const staticUrl = useRuntimeConfig().public.staticUrl
const useModalStoreInstance = useModalStore()
const { showClubWorldCupFinalRuleModal } = storeToRefs(useModalStoreInstance)

const handleClick = () => {
    showClubWorldCupFinalRuleModal.value = true
}
</script>

<style scoped>
.banner-event {
    cursor: pointer;
}
</style>
