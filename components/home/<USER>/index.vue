<template>
    <div
        class="home-hero-banner relative aspect-[43/18.5] pt-[41.86%] lg:aspect-[1915/418.91] lg:pt-[21.875%]"
    >
        <div class="absolute inset-0">
            <Swiper
                :modules="[
                    Autoplay,
                    Pagination,
                    EffectCards,
                    EffectCreative,
                    FreeMode,
                    Navigation,
                    EffectFade,
                ]"
                :preventClicks="false"
                :slides-per-view="1"
                :spaceBetween="0"
                :loop="true"
                :pagination="{
                    clickable: true,
                }"
                :fadeEffect="{
                    crossFade: true,
                }"
                :autoplay="{
                    delay: 5000,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: true,
                }"
                @slideChange="onChangeSlide"
                @swiper="onSwiper"
                :initial-slide="0"
            >
                <SwiperSlide
                    v-for="(heroBanner, index) in heroBanners"
                    :key="index"
                >
                    <component
                        v-if="currentSlideActive === index && !isMounted"
                        :is="heroBanner.component"
                    />
                    <component v-if="isMounted" :is="heroBanner.component" isHome />
                </SwiperSlide>
            </Swiper>
        </div>
    </div>
</template>
<script setup lang="ts">
import {
    Autoplay,
    Pagination,
    EffectCards,
    EffectCreative,
    FreeMode,
    Navigation,
    EffectFade,
} from 'swiper/modules'
// import BannerLodeDefault from '~/components/home/<USER>/banner-lottery/index.vue'
// import BannerFreeSpin from '~/components/home/<USER>/banner-free-spin/index.vue'
// import BannerNoel2024 from '~/components/home/<USER>/banner-noel-2024/index.vue'
import BannerCasino from '~/components/home/<USER>/banner-casino/index.vue'
import BannerPromotions from '~/components/home/<USER>/banner-promotions/index.vue'
// import BannerFreeSpinTet from '~/components/home/<USER>/banner-free-spin-tet/index.vue'
// import BannerVeSoCao from '~/components/home/<USER>/banner-ve-so-cao/index.vue'
// import BannerBongVang from '~/components/home/<USER>/banner-bong-vang/index.vue'
import BannerJackpot from '~/components/home/<USER>/banner-jackpot/index.vue'
import BannerSvip from '~/components/home/<USER>/banner-svip/index.vue'
// import OlympicBanner from '~/components/events/fifaclub/banner.vue'
import BannerDanhDeMienPhi from '~/components/home/<USER>/banner-danh-de-mien-phi/index.vue'
import BannerBaccarat from '~/components/home/<USER>/banner-baccarat/index.vue'
// import BannerClubWcFinal from '~/components/home/<USER>/banner-club-wc-final/index.vue'
import BannerInsuranceSport from '~/components/home/<USER>/banner-insurance-sport/index.vue'

const swiperRef = ref<SwiperCore | null>(null)
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const socketStoreInstance = useSocket()
const { jackpotGo88, prevJackpotGo88 } = storeToRefs(socketStoreInstance)

const heroBanners = computed(() => {
    const jackpot = {
        id: 'banner-jackpot',
        component: BannerJackpot,
        name: 'aff-2024',
    }
    let bannerOfUSer = [
        // {
        //     id: 'club-wc-final',
        //     component: BannerClubWcFinal,
        //     name: 'club-wc-final',
        // },
        {
            id: 'danh-de-mien-phi',
            component: BannerDanhDeMienPhi,
            name: 'danh-de-mien-phi',
        },
        // {
        //     id: 'fifa-club',
        //     component: OlympicBanner,
        //     name: 'fifa-club',
        // },
        {
            id: 'svip',
            component: BannerSvip,
            name: 'svip',
        },
        // {
        //     id: 'bong-vang',
        //     component: BannerBongVang,
        //     name: 'bong-vang',
        // },
        // {
        //     id: 've-so-cao',
        //     component: BannerVeSoCao,
        //     name: 've-so-cao',
        // },
        // {
        //     id: 'free-spin-tet',
        //     component: BannerFreeSpinTet,
        //     name: 'free-spin-tet',
        // },
        // {
        //     id: 'noel-2024',
        //     component: BannerNoel2024,
        //     name: 'noel-2024',
        // },
        // {
        //     id: 'free-spin',
        //     component: BannerFreeSpin,
        //     name: 'free-spin',
        // },
        {
            id: 'baccarat',
            component: BannerBaccarat,
            name: 'baccarat',
        },
        {
            id: 'promotions',
            component: BannerPromotions,
            name: 'promotions',
        },
        { id: 'casino', component: BannerCasino, name: 'Casino' },
        // {
        //     id: 'lode-default',
        //     component: BannerLodeDefault,
        //     name: 'Lode Default',
        // },
        {
            id: 'insurance-sport',
            component: BannerInsuranceSport,
            name: 'insurance-sport',
        },
    ]
    if (user.value && user.value.is_commission === true) {
        bannerOfUSer = bannerOfUSer.filter((item) => item.id !== 'promotions')
    }
    return jackpotGo88.value?.length ? [ ...bannerOfUSer] : bannerOfUSer
})

const isMounted = ref(false)

const currentSlideActive = ref(0)

const onChangeSlide = (event: SwiperCore) => {
    currentSlideActive.value = event.realIndex
}

const onSwiper = (swiperInstance: SwiperCore) => {
    swiperRef.value = swiperInstance
}
onMounted(() => {
    nextTick(() => {
        isMounted.value = true
    })
})

watch(
    () => jackpotGo88.value,
    (jackpots) => {
        const jackpotsGameIds = jackpots?.map((item) => item.gameId) || []
        const isDifferent =
            jackpots?.length &&
            (jackpots?.length > prevJackpotGo88.value?.length ||
                prevJackpotGo88.value.some(
                    (item) => !jackpotsGameIds.includes(item.gameId)
                ))

        if (isDifferent) {
            if (typeof window !== 'undefined') {
                window.scrollTo(0, 0)
            }

            if (swiperRef.value) {
                swiperRef.value.slideTo(0)
            }
        }
    },
    { immediate: true, deep: true }
)
</script>
