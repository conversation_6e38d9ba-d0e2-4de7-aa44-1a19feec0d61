import { useModalStore } from '~/stores'
export default defineNuxtRouteMiddleware((to, from) => {
    const useModalStoreInstance = useModalStore()
    if (process.server) {
        const user = useCookie('user')
        if (!user.value) {
            abortNavigation()
            return navigateTo('/')
        }
    } else {
        const userStoreFromStorage = localStorage.getItem('userStore')
        const userStore = JSON.parse(userStoreFromStorage)
        const user = userStore?.user

        if (!user) {
            abortNavigation()
            useModalStoreInstance.showAuthModal()
            // await useUserStoreInstance.refresh()
            if (from.path.includes('user')) {
                return (window.location = '/')
            } else {
                return navigateTo(from.path)
            }
        }
    }
})
