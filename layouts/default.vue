<template>
    <div
        class="wrapper flex min-h-screen flex-col"
        :style="{ minHeight: `${innerHeight}px` }"
    >
        <LayoutHeader />
        <main :class="['has-main-nav flex-1']">
            <slot />
        </main>
        <LazyLayoutFooter />
        <ClientOnly>
            <ModalGameList v-show="showGameList" />
            <LayoutBottomBar v-if="!isDeskTop && isShowBottomBar" />
            <LazyModalInsuranceSportAmount
                :show="currentModal === PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT"
            />
            <template v-if="!disableUserModal">
                <LazyModalLogin :show="(showLoginModal || showRegisterModal) && !currentModal" />
                <LazyModalRegisterSuccess :show="showRegisterSuccessModal" />
            </template>
            <LazyModalForgotPassword :show="showForgotPasswordModal" />
            <LazyModalRenewPasswordTelegram
                :show="showRenewPasswordTelegramModal"
            />
            <LazyModalConfirmLogout :show="showModalConfirmLogout" />
            <LazyModalLoginFail :show="showLoginFailModal" />
            <LazyCommonLivechat />
            <LazyModalUpdateFullname v-show="showUpdateFullNameModal" />
            <LazyModalWarningDeposit v-show="showWarningDeposit" />
            <!-- <LazyModalBongVang
                :show="currentModal === PROMOTION_POPUP_METHOD.LD_BONG_VANG"
            /> -->
            <!-- <LazyModalFifaClub
                :show="currentModal === PROMOTION_POPUP_METHOD.LD_FIFA_CLUB && !showUefaChampionBannerModal && !showClubWorldCupFinalRuleModal"
            /> -->
            <LazyModalFifaClubCashback
                :show="
                    currentModal ===
                    PROMOTION_POPUP_METHOD.LD_FIFA_CLUB_CASHBACK
                "
            />
            <LazyModalGameMaintain v-show="showGameMaintain" />
            <LazyModalWarningPromotion v-show="showWarningPromotion" />
            <LazyModalNotify
                :show="configNotification.show"
                :image="`${staticUrl}${configNotification.image}`"
                :title="configNotification.title"
                :subTitle="configNotification.subTitle"
                :suggestion="configNotification.suggestion"
                :cancelBtn="configNotification.cancelBtn"
                :acceptBtn="configNotification.acceptBtn"
                @onCancel="configNotification.onCancel"
                @acceptBtn="configNotification.onAccept"
            />
            <ModalUserPromotionsCancelPromotion v-show="showPromotionsCancel" />
            <ModalPromotionWarning v-show="showPromotionsCancelWarning" />
            <LazyModalVipInfoLevelUp
                :show="currentModal === PROMOTION_POPUP_METHOD.LD_VIP_RANKING"
            />
            <ModalVipInfoCashback
                :show="currentModal === PROMOTION_POPUP_METHOD.LD_VIP_CASHBACK"
            />
            <LazyModalDanhDeMienPhiCashbackAward
                :show="currentModal === PROMOTION_POPUP_METHOD.LUCKY_DRAW"
            />
            <!-- <LazyModalClubWcFinalRule :show="showClubWorldCupFinalRuleModal" />
            <LazyModalClubWcFinalRuleNotify
                :show="showUefaChampionBannerModal"
            /> -->
            <LazyModalClubWcFinalRuleAward
                :show="currentModal === CLUB_WC_CHAMPION_LEAGUE_METHOD"
            />
            <div id="modals"></div>
        </ClientOnly>
        <ClientOnly>
            <MiniGame />
            <FloatingIcon />
            <FloatingLuckydraw />
        </ClientOnly>
        <div v-if="!visibleEventTicket" class="hidden">
            <h3>
                {{ $t('common.opportunity') }}
            </h3>
            <h2>
                {{ $t('common.totalBetting') }}
            </h2>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { usePromotion } from '~/composables/use-promotion'
import { useModalStore } from '~/stores'
import { MODAL_NAME } from '~/constants/common'
import {
    FOOTER_HIDDEN_MOBILE_ROUTES,
    PROMOTION_POPUP_METHOD,
} from '~/constants/common'
import { useUserStore } from '~/composables/use-user'
import { DISABLE_USER_MODAL } from '~/resources/header'
import { useVVipStore } from '~/stores/vvip'
import { useVipProfile } from '~/composables/use-vip-profile'
import useCountdown from '~/composables/common/use-countdown'
import dayjs from 'dayjs'

const { fetchVipProfile } = useVipProfile()
const useVVipStoreInstance = useVVipStore()
const { getConfig } = useVVipStoreInstance
const { staticUrl } = useRuntimeConfig().public
const { innerHeight, isDeskTop } = useWindowSize()
const route = useRoute()
const useModalStoreInstance = useModalStore()
const { queueModal } = useModalStoreInstance
const CLUB_WC_CHAMPION_LEAGUE_METHOD =
    useRuntimeConfig().public.CLUB_WC_CHAMPION_LEAGUE_METHOD

const {
    showLoginModal,
    showRegisterModal,
    showRegisterSuccessModal,
    showForgotPasswordModal,
    showRenewPasswordTelegramModal,
    showModalConfirmLogout,
    currentModal,
    showLoginFailModal,
    showUpdateFullNameModal,
    showWarningDeposit,
    showGameMaintain,
    showWarningPromotion,
    configNotification,
    showPromotionsCancel,
    showPromotionsCancelWarning,
    showGameList,
    showClubWorldCupFinalRuleModal,
    showUefaChampionModal,
    noShowAgainUefaChampionModal,
    showUefaChampionBannerModal,
    noShowAgainUefaChampionBannerModal,
} = storeToRefs(useModalStoreInstance)
const useUserStoreInstance = useUserStore()
const { user, isLogged } = storeToRefs(useUserStoreInstance)
const { refreshInterval, loginToken } = useUserStore()
const useServerTimeInstance = useServerTime()
const { getServerTime } = useServerTimeInstance
const { isBetweenDate: isUefaEventActive } = useCountdown(
    dayjs
        .tz(useRuntimeConfig().public.CLUB_WC_EVENT_START_DAY, 'Asia/Ho_Chi_Minh')
        .format(),
    dayjs
        .tz(useRuntimeConfig().public.CLUB_WC_EVENT_END_DAY, 'Asia/Ho_Chi_Minh')
        .format()
)
const disableUserModal = computed(() => {
    return DISABLE_USER_MODAL.some((el) => el === route.name)
})
const { getPromotionPopup } = usePromotion()

const visibleEventTicket = computed(() => {
    return !user
})

const checkAndShowPopup = (key: string, method: string) => {
    const savedRaw = localStorage.getItem(key)
    if (savedRaw) {
        try {
            const saved = JSON.parse(savedRaw)
            const hoursPassed =
                (Date.now() - saved.timestamp) / (1000 * 60 * 60)
            if (saved.value === false && hoursPassed >= 48) {
                queueModal(method)
            }
        } catch (e) {
            localStorage.removeItem(key)
            queueModal(method)
        }
    } else {
        queueModal(method)
    }
}
onMounted(async () => {
    await nextTick(async () => {
        if (route?.path === '/' && route?.query?.token) {
            await loginToken({ token: route?.query?.token })
            const wlPath = ['/lo-dien-vua-keo']
            if(wlPath?.indexOf(route?.query?.redirect) !== -1){
                window.location.href = route?.query?.redirect;
            }
        }
    })
    // checkAndShowPopup('saveBongVangPopup', PROMOTION_POPUP_METHOD.LD_BONG_VANG)
    // checkAndShowPopup('saveFifaClubPopup', PROMOTION_POPUP_METHOD.LD_FIFA_CLUB)
    if (route.query.homeUrl === 'login' && user.value === null) {
        showLoginModal.value = true
    }
    if (route.query.homeUrl === 'register' && user.value === null) {
        showRegisterModal.value = true
    }
    if (user.value) {
        await nextTick()
        Promise.all([refreshInterval(), getConfig()])
        fetchVipProfile()
        getPromotionPopup()
    }
    if (typeof window !== 'undefined') {
        window.addEventListener('storage', function (event) {
            if (event.key === 'reload') {
                const _reload = JSON.parse(localStorage.getItem('reload') || '')
                if (_reload) {
                    reloadNuxtApp()
                    this.localStorage.removeItem('reload')
                }
            }
        })
    }
})

watch(
    () => isLogged.value,
    (value) => {
        if (value) {
            Promise.all([refreshInterval(), getConfig()])
            fetchVipProfile()
        }
    }
)

const headerHiddenRoutes = ['maintain']

const isShowBottomBar = computed(() => {
    const _routeName = route.name?.toString() || ''
    return !headerHiddenRoutes.includes(_routeName)
})
import { useRouteParams } from '@vueuse/router'
import { LazyModalDanhDeMienPhiCashback } from '#components'

import { useServerTime } from '~/composables/common/time-server'
const slug = useRouteParams('slug', '', { transform: String })
const isShowLiveChat = computed(() => {
    const routers = ['lo-de-mien-bac', 'lo-de-mien-trung', 'lo-de-mien-nam']
    return !(routers.includes(slug.value) || route.name === 'lo-de-ba-mien')
})
watch(
    () => route.query,
    (query) => {
        if (!query?.modal || user.value) {
            return
        }
        const _modal = Array.isArray(query?.modal)
            ? query?.modal[0]
            : query?.modal
        const modalName = _modal?.split('?')[0]
        if (modalName === MODAL_NAME.LOGIN) {
            showLoginModal.value = true
            return
        }
        if (modalName === MODAL_NAME.REGISTER) {
            showRegisterModal.value = true
        }
    },
    { immediate: true }
)

const showAgainHours = Number(
    useRuntimeConfig().public.CLUB_WC_MODAL_SHOW_AGAIN_HOURS
)
const showAgainMilliseconds = showAgainHours * 60 * 60 * 1000
onMounted(() => {
    useModalStoreInstance.initShowAgainUefaChampionModal()
    useModalStoreInstance.initShowAgainUefaChampionBannerModal()
    nextTick(() => {
        if (!isUefaEventActive.value) {
            showUefaChampionModal.value = false
            showUefaChampionBannerModal.value = false
            return
        }
        if (
            noShowAgainUefaChampionModal.value &&
            useModalStoreInstance.uefaChampionModalCheckTime
        ) {
            const checkTime = dayjs(
                useModalStoreInstance.uefaChampionModalCheckTime
            ).tz('Asia/Ho_Chi_Minh')
            const now = getServerTime()
            const timePassed = now.diff(checkTime)
            if (timePassed >= showAgainMilliseconds) {
                useModalStoreInstance.setShowAgainUefaChampionModal(false)
                showUefaChampionModal.value = true
            } else {
                const remainingTime = showAgainMilliseconds - timePassed
                setTimeout(() => {
                    useModalStoreInstance.setShowAgainUefaChampionModal(false)
                    showUefaChampionModal.value = true
                }, remainingTime)
            }
        } else {
            showUefaChampionModal.value = true
        }
        if (
            noShowAgainUefaChampionBannerModal.value &&
            useModalStoreInstance.uefaChampionBannerModalCheckTime
        ) {
            const checkTime = dayjs(
                useModalStoreInstance.uefaChampionBannerModalCheckTime
            ).tz('Asia/Ho_Chi_Minh')
            const now = getServerTime()
            const timePassed = now.diff(checkTime)
            if (timePassed >= showAgainMilliseconds) {
                useModalStoreInstance.setShowAgainUefaChampionBannerModal(false)
                showUefaChampionBannerModal.value = true
            } else {
                const remainingTime = showAgainMilliseconds - timePassed
                setTimeout(() => {
                    useModalStoreInstance.setShowAgainUefaChampionBannerModal(
                        false
                    )
                    showUefaChampionBannerModal.value = true
                }, remainingTime)
            }
        } else {
            showUefaChampionBannerModal.value = true
        }
    })
})
watch(
    () => isUefaEventActive.value,
    (newValue) => {
        if (!newValue) {
            showUefaChampionModal.value = false
            showUefaChampionBannerModal.value = false
        }
    }
)
watch(
    () => showUefaChampionBannerModal.value,
    (newValue) => {
        if (!newValue && !useModalStoreInstance.uefaChampionModalCheckTime) {
            showUefaChampionModal.value = true
        } else {
            showUefaChampionModal.value = false
        }
    }
)
</script>
